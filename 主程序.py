# 修复版main.py - 解决打包后的导入问题
import sys
import os

# 添加当前目录到Python路径
if hasattr(sys, '_MEIPASS'):
    # PyInstaller打包后的临时目录
    base_path = sys._MEIPASS
else:
    # 开发环境
    base_path = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, base_path)

# 导入所有必要的模块
try:
    from flask import Flask, render_template, request, jsonify, send_file
    import json
    import requests
    import os
    from datetime import datetime, timedelta
    from urllib.parse import quote, quote_plus, urlparse, parse_qs
    import io
    import threading
    import queue
    import time
    from PIL import Image
    import tempfile
    import zipfile
    import pyperclip
    import webbrowser
    import tkinter as tk
    from tkinter import messagebox
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.primitives import hashes, padding
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    import configparser
    from DrissionPage import Chromium
except ImportError as e:
    print(f"导入模块失败: {e}")
    input("按回车键退出...")
    sys.exit(1)

# 设置模板路径
if hasattr(sys, '_MEIPASS'):
    # PyInstaller打包后的模板路径
    template_folder = os.path.join(sys._MEIPASS, 'templates')
    app = Flask(__name__, template_folder=template_folder)
else:
    # 开发环境
    app = Flask(__name__)

class ConfigManager:
    """配置文件管理类"""

    def __init__(self, config_file='配置.ini'):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()

    def load_config(self):
        """加载配置文件，如果不存在则创建空白配置"""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file, encoding='utf-8')
            except Exception as e:
                print(f"读取配置文件失败: {e}")
                self.create_default_config()
        else:
            self.create_default_config()

    def create_default_config(self):
        """创建默认空白配置文件"""
        self.config.clear()
        # 添加基本配置节
        self.config.add_section('搜索设置')
        self.save_config()

    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def get_value(self, section, key, default=''):
        """获取配置值"""
        try:
            return self.config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default

    def set_value(self, section, key, value):
        """设置配置值并保存"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, str(value))
        self.save_config()

    def get_search_path(self):
        """获取搜索路径"""
        return self.get_value('搜索设置', '搜索路径', '')

    def set_search_path(self, path):
        """设置搜索路径"""
        self.set_value('搜索设置', '搜索路径', path)

    def get_strict_search(self):
        """获取严格搜索设置"""
        value = self.get_value('搜索设置', '严格搜索', 'true')
        return value.lower() == 'true'

    def set_strict_search(self, strict):
        """设置严格搜索"""
        self.set_value('搜索设置', '严格搜索', 'true' if strict else 'false')

    def get_ignore_keywords(self):
        """获取忽略关键词列表"""
        keywords_str = self.get_value('搜索设置', '忽略关键词', '')
        if not keywords_str.strip():
            return []
        # 按行分割，去除空行和首尾空格
        keywords = [line.strip() for line in keywords_str.split('\n') if line.strip()]
        return keywords

    def set_ignore_keywords(self, keywords_list):
        """设置忽略关键词列表"""
        if isinstance(keywords_list, list):
            keywords_str = '\n'.join(keywords_list)
        else:
            keywords_str = str(keywords_list)
        self.set_value('搜索设置', '忽略关键词', keywords_str)

class JsonDataFetcher:
    """JSON数据拉取功能模块"""

    def __init__(self):
        self.log_messages = []

    def log(self, message, message_type='info'):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_messages.append({
            'time': timestamp,
            'message': message,
            'type': message_type
        })
        print(f"[{timestamp}] {message}")

    def create_save_directory(self):
        """创建保存目录"""
        save_dir = "订单管理API响应数据"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        return save_dir

    def save_response_to_file(self, data, filename_prefix="订单API响应"):
        """保存响应数据到文件"""
        save_dir = self.create_save_directory()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.json"
        filepath = os.path.join(save_dir, filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4, ensure_ascii=False)
            self.log(f"✅ 数据已保存到: {filepath}", 'success')
            return filepath
        except Exception as e:
            self.log(f"❌ 保存文件失败: {str(e)}", 'error')
            return None

    def open_target_page_and_monitor(self):
        """打开目标页面并监控API请求"""
        try:
            self.log("🌐 正在启动浏览器...", 'info')
            browser = Chromium(9222)
            new_tab = browser.new_tab()
            new_tab.set.activate()

            target_api = "api/seller/auth/menu"
            new_tab.listen.start(targets=target_api)

            target_url = "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"
            self.log(f"🔗 正在访问页面: {target_url}", 'info')
            new_tab.get(target_url)

            try:
                packet = new_tab.listen.wait(timeout=3)
                if packet:
                    self.log("📦 捕获到早期API请求", 'success')
                    return new_tab, packet
            except:
                pass

            new_tab.wait.load_start()

            try:
                packet = new_tab.listen.wait(timeout=2)
                if packet:
                    self.log("📦 捕获到加载期API请求", 'success')
                    return new_tab, packet
            except:
                pass

            new_tab.wait.doc_loaded()
            self.log("📄 页面加载完成", 'info')
            return new_tab, None

        except Exception as e:
            self.log(f"❌ 浏览器操作失败: {str(e)}", 'error')
            return None, None

    def capture_api_request(self, tab):
        """捕获API请求"""
        try:
            self.log("⏳ 等待API请求...", 'info')

            # 多次尝试捕获请求
            for timeout in [3, 5, 10]:
                try:
                    packet = tab.listen.wait(timeout=timeout)
                    if packet:
                        self.log(f"📦 成功捕获API请求 (超时: {timeout}s)", 'success')
                        return packet
                except Exception:
                    continue

            self.log("⚠️ 未能捕获到API请求", 'warning')
            return None
        except Exception as e:
            self.log(f"❌ 捕获API请求异常: {str(e)}", 'error')
            return None

    def extract_auth_headers(self, packet):
        """提取认证头信息"""
        if not packet or not packet.request:
            self.log("❌ 无效的请求包", 'error')
            return None

        auth_headers = {}
        important_headers = [
            'Anti-Content', 'mallid', 'cookie', 'authorization',
            'x-csrf-token', 'x-requested-with', 'user-agent',
            'accept', 'accept-language', 'content-type',
            'origin', 'referer'
        ]

        if packet.request.headers:
            for key, value in packet.request.headers.items():
                if any(important_header.lower() == key.lower() for important_header in important_headers):
                    clean_key = key.strip()
                    clean_value = str(value).strip()
                    if clean_key and clean_value:
                        auth_headers[clean_key] = clean_value

        auth_data = {
            'extraction_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'original_url': packet.url,
            'method': packet.method,
            'headers': auth_headers,
            'original_post_data': packet.request.postData if packet.request.postData else None,
            'description': '从真实API请求中提取的重要认证信息'
        }

        self.save_response_to_file(auth_data, "提取的认证信息")
        self.log(f"🔑 成功提取 {len(auth_headers)} 个认证头", 'success')
        return auth_headers

    def send_custom_api_request(self, tab, auth_headers):
        """发送自定义API请求"""
        if not auth_headers:
            self.log("❌ 缺少认证头信息", 'error')
            return None

        custom_request_data = {
            "pageNo": 1,
            "pageSize": 100,
            "urgencyType": 1,
            "isCustomGoods": False,
            "statusList": [1],
            "oneDimensionSort": {
                "firstOrderByParam": "expectLatestDeliverTime",
                "firstOrderByDesc": 0
            }
        }

        api_url = "https://agentseller.temu.com/mms/venom/api/supplier/purchase/manager/querySubOrderList"

        self.log("📤 正在发送自定义API请求...", 'info')
        self.log(f"🎯 API地址: {api_url}", 'info')
        self.log(f"📊 请求数据: {json.dumps(custom_request_data, ensure_ascii=False)}", 'info')

        try:
            js_code = f'''
            (async function() {{
                try {{
                    const response = await fetch('{api_url}', {{
                        method: 'POST',
                        headers: {json.dumps(auth_headers)},
                        body: JSON.stringify({json.dumps(custom_request_data)})
                    }});

                    const responseText = await response.text();

                    const result = {{
                        status: response.status,
                        statusText: response.statusText,
                        headers: Object.fromEntries(response.headers.entries()),
                        body: responseText,
                        success: true
                    }};

                    window.customOrderApiResponse = result;
                    return result;
                }} catch (error) {{
                    const errorResult = {{
                        error: error.message,
                        success: false
                    }};
                    window.customOrderApiResponse = errorResult;
                    return errorResult;
                }}
            }})();
            '''

            tab.run_js('window.customOrderApiResponse = null;')
            tab.run_js(js_code)

            max_wait_time = 10
            wait_interval = 0.5

            for _ in range(int(max_wait_time / wait_interval)):
                time.sleep(wait_interval)
                try:
                    response_data = tab.run_js('return window.customOrderApiResponse;')
                    if response_data is not None:
                        break
                except Exception:
                    pass
            else:
                self.log("⏰ API请求超时", 'error')
                return None

            if response_data:
                if not response_data.get('success', True) or 'error' in response_data:
                    self.log(f"❌ API请求失败: {response_data.get('error', '未知错误')}", 'error')
                    return None

                self.log(f"✅ API请求成功 (状态码: {response_data.get('status')})", 'success')

                try:
                    response_json = json.loads(response_data['body'])

                    complete_response_data = {
                        'request_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        'api_url': api_url,
                        'request_method': 'POST',
                        'request_data': custom_request_data,
                        'response_status': response_data.get('status'),
                        'response_status_text': response_data.get('statusText'),
                        'response_headers': response_data.get('headers', {}),
                        'response_body': response_json,
                        'description': '订单管理API的完整响应信息'
                    }

                    self.save_response_to_file(complete_response_data, "订单管理API完整响应")
                    self.log("💾 完整响应数据已保存", 'success')

                except Exception as e:
                    self.log(f"⚠️ JSON解析失败: {str(e)}", 'warning')
                    raw_response_data = {
                        'request_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        'api_url': api_url,
                        'request_data': custom_request_data,
                        'response_status': response_data.get('status'),
                        'response_body_raw': response_data['body'],
                        'json_parse_error': str(e),
                        'description': '订单管理API原始响应（JSON解析失败）'
                    }

                    self.save_response_to_file(raw_response_data, "订单管理API原始响应")

                return response_data
            else:
                self.log("❌ 未收到API响应", 'error')
                return None

        except Exception as e:
            self.log(f"❌ 发送API请求异常: {str(e)}", 'error')
            return None

    def fetch_json_data(self):
        """主要的JSON数据拉取流程"""
        self.log_messages = []  # 清空日志
        self.log("🚀 开始拉取JSON数据...", 'info')

        try:
            # 打开页面并监控
            result = self.open_target_page_and_monitor()

            if result[0]:
                tab = result[0]
                early_packet = result[1]

                if early_packet:
                    packet = early_packet
                    self.log("📦 使用早期捕获的请求包", 'info')
                else:
                    packet = self.capture_api_request(tab)
                    if packet:
                        self.log("📦 使用后续捕获的请求包", 'info')

                if packet:
                    # 提取认证头
                    auth_headers = self.extract_auth_headers(packet)

                    if auth_headers:
                        # 发送自定义请求
                        custom_response = self.send_custom_api_request(tab, auth_headers)

                        if custom_response:
                            try:
                                response_body = json.loads(custom_response['body'])
                                self.log("🎉 JSON数据拉取完成！", 'success')
                                return {
                                    'success': True,
                                    'data': response_body,
                                    'logs': self.log_messages
                                }
                            except Exception as e:
                                self.log(f"❌ 解析响应JSON失败: {str(e)}", 'error')
                                return {
                                    'success': False,
                                    'message': f'解析响应JSON失败: {str(e)}',
                                    'logs': self.log_messages
                                }
                        else:
                            return {
                                'success': False,
                                'message': '发送API请求失败',
                                'logs': self.log_messages
                            }
                    else:
                        return {
                            'success': False,
                            'message': '提取认证头失败',
                            'logs': self.log_messages
                        }
                else:
                    return {
                        'success': False,
                        'message': '未能捕获到API请求',
                        'logs': self.log_messages
                    }

                # 停止监听
                tab.listen.stop()
            else:
                return {
                    'success': False,
                    'message': '浏览器操作失败',
                    'logs': self.log_messages
                }

        except Exception as e:
            self.log(f"❌ 拉取JSON数据异常: {str(e)}", 'error')
            return {
                'success': False,
                'message': f'拉取JSON数据异常: {str(e)}',
                'logs': self.log_messages
            }

class ImageDownloader:
    """图片下载功能模块"""

    @staticmethod
    def format_size(bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 MB"
        mb_size = bytes_size / (1024 * 1024)
        if mb_size < 1024:
            return f"{mb_size:.2f} MB"
        return f"{mb_size / 1024:.2f} GB"

    @classmethod
    def should_download_file(cls, file_path, file_name):
        """检查文件是否符合下载条件"""
        return True  # 不再检查文件扩展名

    @classmethod
    def prepare_directory(cls, base_dir):
        """准备下载目录"""
        os.makedirs(base_dir, exist_ok=True)
        return base_dir

class ImageProcessor:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.current_output_dir = ""
        self.log_messages = []

    def log(self, message, message_type='info'):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_messages.append({
            'time': timestamp,
            'message': message,
            'type': message_type
        })
        print(f"[{timestamp}] {message}")

    def remove_ignore_keywords(self, product_name):
        """从商品名称中移除忽略关键词"""
        ignore_keywords = self.config_manager.get_ignore_keywords()
        if not ignore_keywords:
            return product_name

        cleaned_name = product_name
        for keyword in ignore_keywords:
            if keyword:  # 确保关键词不为空
                # 使用不区分大小写的替换
                cleaned_name = cleaned_name.replace(keyword, '')
                cleaned_name = cleaned_name.replace(keyword.lower(), '')
                cleaned_name = cleaned_name.replace(keyword.upper(), '')
                cleaned_name = cleaned_name.replace(keyword.capitalize(), '')

        # 清理多余的空格
        cleaned_name = ' '.join(cleaned_name.split())

        if cleaned_name != product_name:
            self.log(f"🔄 关键词过滤: '{product_name}' → '{cleaned_name}'", 'info')

        return cleaned_name

    def get_fulfilment_id(self, order):
        """获取fulfilmentProductSkuId"""
        try:
            sku_list = order.get('skuQuantityDetailList', [])
            if not sku_list:
                self.log("⚠️ skuQuantityDetailList为空", 'warning')
                return "无FulfilmentID"
            return str(sku_list[0].get('fulfilmentProductSkuId', '无FulfilmentID'))
        except Exception as e:
            self.log(f"获取fulfilmentID异常: {str(e)}", 'error')
            return "错误ID"

    def download_image(self, url, save_path):
        """下载图片文件"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/'
        }

        try:
            response = requests.get(url, headers=headers, stream=True, timeout=15)
            response.raise_for_status()

            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(1024):
                        f.write(chunk)
                return True
            return False
        except Exception as e:
            self.log(f"下载异常: {str(e)}", 'error')
            return False

    def detect_image_extension(self, url):
        """根据URL猜测图片扩展名"""
        if 'jpeg' in url.lower() or 'jpg' in url.lower():
            return '.jpg'
        elif 'png' in url.lower():
            return '.png'
        elif 'gif' in url.lower():
            return '.gif'
        elif 'webp' in url.lower():
            return '.webp'
        return '.jpg'

    def download_api_image(self, url, save_path, max_retries=3, retry_interval=2):
        """下载API图片，带重试机制"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
        }
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.log(f"🔄 第 {attempt + 1} 次重试下载API图片...", 'info')
                    time.sleep(retry_interval)
                
                response = requests.get(url, headers=headers, stream=True, timeout=15)
                response.raise_for_status()
                
                if response.status_code == 200:
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(1024):
                            f.write(chunk)
                    self.log(f"✅ API图片下载成功", 'success')
                    return True
            except Exception as e:
                self.log(f"❌ 第 {attempt + 1} 次下载API图片失败: {str(e)}", 'error')
        
        return False

    def search_local_images(self, product_name, search_path="", strict_search=True):
        """搜索本地图片"""
        # 首先移除忽略关键词
        filtered_name = self.remove_ignore_keywords(product_name)
        # 然后移除"2D Flat "前缀
        cleaned_name = filtered_name.replace("2D Flat ", "").strip()
        search_query = cleaned_name
        
        if search_path and strict_search:
            if not search_path.endswith('\\'):
                search_path += '\\'
            search_query = f"path:\"{search_path}\" {search_query}"
                
        search_params = {
            "search": search_query,
            "json": 1,
            "path_column": 1,
            "size_column": 1,
            "sort": "name",
            "ascending": 1
        }
        
        search_url = "http://localhost:8080/"
        
        try:
            response = requests.get(search_url, params=search_params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            valid_files = []
            for item in data.get("results", []):
                file_name = item.get('name', '')
                file_path = f"{item.get('path', '')}\\{file_name}".replace("\\\\", "\\")
                
                if ImageDownloader.should_download_file(file_path, file_name):
                    valid_files.append({
                        'name': file_name,
                        'path': file_path,
                        'size': item.get('size', 0),
                        'url': f"http://127.0.0.1:8080/{quote(file_path)}"
                    })
            
            return valid_files
            
        except Exception as e:
            self.log(f"❌ 搜索失败: {str(e)}", 'error')
            return []

    def get_product_api_image(self, product_id, json_data):
        """获取商品的API图片URL"""
        try:
            raw_orders = json_data.get('result', {}).get('subOrderForSupplierList', [])
            
            for order in raw_orders:
                if not order.get('isFirst'):
                    continue
                    
                sku_list = order.get('skuQuantityDetailList', [])
                if not sku_list:
                    continue
                    
                sku_id = str(sku_list[0].get('fulfilmentProductSkuId', ''))
                if sku_id == product_id:
                    image_url = order.get('productSkcPicture', '')
                    if image_url:
                        return image_url
            
            return None
        except Exception as e:
            self.log(f"❌ 获取API图片URL失败: {str(e)}", 'error')
            return None

    def process_json_data(self, json_str):
        """处理JSON数据"""
        try:
            data = json.loads(json_str)
            raw_orders = data.get('result', {}).get('subOrderForSupplierList', [])
            original_count = len(raw_orders)

            sub_orders = [order for order in raw_orders if order.get('isFirst')]
            first_order_count = len(sub_orders)

            self.log(f"订单统计: 原始订单数={original_count} | 首单数={first_order_count}", 'info')

            if not sub_orders:
                return None, "未找到首单订单数据"

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folder_name = f"商品数据_{timestamp}_共{first_order_count}组"
            os.makedirs(folder_name, exist_ok=True)
            self.current_output_dir = folder_name

            products = []
            success_count = 0

            for index, order in enumerate(sub_orders, 1):
                try:
                    product_name = order.get('productName', '未知商品').strip()
                    sku_id = self.get_fulfilment_id(order)
                    image_url = order.get('productSkcPicture', '').strip()

                    products.append({
                        'name': product_name,
                        'id': sku_id,
                        'api_image_url': image_url
                    })

                    if image_url and sku_id not in ["无FulfilmentID", "错误ID"]:
                        ext = self.detect_image_extension(image_url)
                        img_path = os.path.join(folder_name, f"{sku_id}{ext}")

                        if self.download_image(image_url, img_path):
                            success_count += 1

                except Exception as e:
                    self.log(f"第{index}条处理失败: {str(e)}", 'error')
                    continue

            # 保存商品列表
            txt_path = os.path.join(folder_name, "商品列表.txt")
            with open(txt_path, 'w', encoding='utf-8') as f:
                for product in products:
                    f.write(f"{product['name']}----{product['id']}\n")

            return products, f"解析完成！成功处理: {success_count}/{first_order_count}"

        except Exception as e:
            return None, f"处理失败: {str(e)}"

def validate_key():
    """验证 key.vdf 文件的有效性"""
    # 获取正确的key.vdf路径
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包后，key.vdf应该与exe在同一目录
        exe_dir = os.path.dirname(sys.executable)
        key_file = os.path.join(exe_dir, "key.vdf")
    else:
        # 开发环境
        key_file = "key.vdf"
    
    if not os.path.exists(key_file):
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("错误", f"未找到授权文件 {key_file}")
            root.destroy()
        except:
            print(f"错误: 未找到授权文件 {key_file}")
        sys.exit(1)

    # 密钥生成配置（必须与加密时一致）
    password = os.getenv('KEY_PASSWORD', b'my_super_secret_password')
    salt = os.getenv('KEY_SALT', b'fixed_salt_value')

    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = kdf.derive(password)

    # 读取加密文件
    try:
        with open(key_file, "rb") as f:
            data = f.read()
            iv = data[:16]
            ciphertext = data[16:]

        # 解密数据
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()

        # 移除填充
        unpadder = padding.PKCS7(128).unpadder()
        current_time_bytes = unpadder.update(padded_data) + unpadder.finalize()

        # 验证时间有效性
        stored_time = datetime.fromisoformat(current_time_bytes.decode('utf-8'))

        # 有效期验证（30天）
        if datetime.now() - stored_time > timedelta(days=30):
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("错误", "软件授权已过期")
                root.destroy()
            except:
                print("错误: 软件授权已过期")
            sys.exit(1)
            
    except Exception as e:
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("错误", f"授权验证失败: {str(e)}")
            root.destroy()
        except:
            print(f"错误: 授权验证失败: {str(e)}")
        sys.exit(1)

# 全局处理器实例
processor = ImageProcessor()
json_fetcher = JsonDataFetcher()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/parse_json', methods=['POST'])
def parse_json():
    """解析JSON数据"""
    try:
        data = request.get_json()
        json_str = data.get('json_data', '')
        
        if not json_str:
            return jsonify({'success': False, 'message': '请提供JSON数据'})
        
        # 清空之前的日志
        processor.log_messages = []
        
        products, message = processor.process_json_data(json_str)
        
        if products is None:
            return jsonify({
                'success': False, 
                'message': message,
                'logs': processor.log_messages
            })
        
        return jsonify({
            'success': True,
            'message': message,
            'products': products,
            'output_dir': processor.current_output_dir,
            'logs': processor.log_messages
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'系统异常: {str(e)}'})

@app.route('/api/search_images', methods=['POST'])
def search_images():
    """搜索本地图片，并对API图片URL进行代理缩放"""
    try:
        data = request.get_json()
        product_name = data.get('product_name', '')
        product_id = data.get('product_id', '')
        search_path = data.get('search_path', processor.config_manager.get_search_path())
        strict_search = data.get('strict_search', processor.config_manager.get_strict_search())
        json_data = data.get('json_data', {})
        
        if not product_name:
            return jsonify({'success': False, 'message': '请提供商品名称'})
        
        # 搜索本地图片 (URL保持不变)
        local_images = processor.search_local_images(product_name, search_path, strict_search)
        
        # 获取API图片URL
        api_image_url_raw = None
        if json_data and product_id:
            api_image_url_raw = processor.get_product_api_image(product_id, json_data)
        
        # 仅对API图片URL进行代理
        api_image_url_proxied = None
        if api_image_url_raw:
            api_image_url_proxied = f"/api/image_proxy?url={quote_plus(api_image_url_raw)}"
            
        return jsonify({
            'success': True,
            'local_images': local_images,
            'api_image_url': api_image_url_proxied,
            'api_image_url_raw': api_image_url_raw,
            'total_count': len(local_images)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'搜索失败: {str(e)}'})

@app.route('/api/download_selected', methods=['POST'])
def download_selected():
    """下载选中的图片"""
    try:
        data = request.get_json()
        product_id = data.get('product_id', '')
        # 前端现在发送 'type', 'url'
        image_url = data.get('url', '')
        
        if not product_id or not image_url:
            return jsonify({'success': False, 'message': '缺少商品ID或图片URL'})
        
        if not processor.current_output_dir:
            return jsonify({'success': False, 'message': '请先解析JSON数据'})
        
        # 准备下载目录
        base_dir = os.path.join(processor.current_output_dir, "出单图下载")
        target_dir = ImageDownloader.prepare_directory(base_dir)

        # 自动为文件名补充图片后缀
        ext = processor.detect_image_extension(image_url)
        new_name = f"{product_id}{ext}"
        local_path = os.path.join(target_dir, new_name)

        # 在新的UI中，只有本地图片可以被选择和下载
        success = processor.download_image(image_url, local_path)

        if success:
            return jsonify({
                'success': True,
                'message': f'图片下载成功: {new_name}',
                'file_path': local_path
            })
        else:
            return jsonify({'success': False, 'message': '图片下载失败'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'})


@app.route('/api/image_proxy')
def image_proxy():
    """图片代理服务，直接返回原图"""
    image_url = request.args.get('url')
    if not image_url:
        return "缺少图片URL参数", 400

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(image_url, headers=headers, stream=True, timeout=15)
        response.raise_for_status()

        # 直接返回原图，不进行缩放处理
        img_data = response.content
        
        # 检测图片格式
        try:
            img = Image.open(io.BytesIO(img_data))
            img_format = img.format if img.format else 'JPEG'
            mimetype = f'image/{img_format.lower()}'
        except:
            # 如果无法检测格式，默认为JPEG
            mimetype = 'image/jpeg'
        
        return send_file(io.BytesIO(img_data), mimetype=mimetype)

    except Exception as e:
        print(f"图片代理错误: {e}")
        return "处理图片时出错", 500


@app.route('/api/get_logs')
def get_logs():
    """获取日志信息"""
    return jsonify({'logs': processor.log_messages})

@app.route('/api/get_clipboard', methods=['GET'])
def get_clipboard():
    """获取剪切板内容"""
    try:
        clipboard_content = pyperclip.paste()
        return jsonify({
            'success': True,
            'content': clipboard_content
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取剪切板内容失败: {str(e)}'
        })

@app.route('/api/get_config', methods=['GET'])
def get_config():
    """获取配置信息"""
    try:
        ignore_keywords = processor.config_manager.get_ignore_keywords()
        return jsonify({
            'success': True,
            'config': {
                'search_path': processor.config_manager.get_search_path(),
                'strict_search': processor.config_manager.get_strict_search(),
                'ignore_keywords': '\n'.join(ignore_keywords)
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取配置失败: {str(e)}'
        })

@app.route('/api/save_config', methods=['POST'])
def save_config():
    """保存配置信息"""
    try:
        data = request.get_json()
        search_path = data.get('search_path', '')
        strict_search = data.get('strict_search', True)
        ignore_keywords_str = data.get('ignore_keywords', '')

        processor.config_manager.set_search_path(search_path)
        processor.config_manager.set_strict_search(strict_search)

        # 处理忽略关键词
        if ignore_keywords_str.strip():
            keywords_list = [line.strip() for line in ignore_keywords_str.split('\n') if line.strip()]
            processor.config_manager.set_ignore_keywords(keywords_list)
        else:
            processor.config_manager.set_ignore_keywords([])

        return jsonify({
            'success': True,
            'message': '配置保存成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'保存配置失败: {str(e)}'
        })

@app.route('/api/fetch_json', methods=['POST'])
def fetch_json():
    """拉取JSON数据"""
    try:
        result = json_fetcher.fetch_json_data()

        if result['success']:
            # 将拉取到的JSON数据转换为字符串，方便前端使用
            json_str = json.dumps(result['data'], ensure_ascii=False, indent=2)
            return jsonify({
                'success': True,
                'json_data': json_str,
                'message': 'JSON数据拉取成功',
                'logs': result['logs']
            })
        else:
            return jsonify({
                'success': False,
                'message': result['message'],
                'logs': result['logs']
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'拉取JSON数据异常: {str(e)}',
            'logs': json_fetcher.log_messages
        })

if __name__ == '__main__':
    try:
        # 验证授权文件
        validate_key()
        
        # 启动Flask应用
        port = 8726
        url = f'http://localhost:{port}'
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(1.5)  # 等待Flask服务启动
            webbrowser.open(url)
        
        # 在新线程中打开浏览器
        threading.Thread(target=open_browser, daemon=True).start()
        
        print(f"商品数据管理工具启动中...")
        print(f"服务地址: {url}")
        print(f"浏览器将自动打开，如未打开请手动访问上述地址")
        
        app.run(debug=False, host='0.0.0.0', port=port, use_reloader=False)
        
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        input("按回车键退出...")